import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { ChevronRight } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SectionHeader } from '@/components/ui/section-header';
import {
  useContractorTranslations,
  useAgencyTranslations,
} from '@/hooks/use-translations';
import { type ContractorStep1FormValues } from '../schemas/contractor-onboarding-schemas';
import { AGENCY_CODES } from '@/lib/constants';

interface ContractorOnboardingStep1Props {
  form: UseFormReturn<ContractorStep1FormValues>;
  onSubmit: (values: ContractorStep1FormValues) => void;
}

export const ContractorOnboardingStep1 =
  React.memo<ContractorOnboardingStep1Props>(({ form, onSubmit }) => {
    const t = useContractorTranslations();
    const tAgency = useAgencyTranslations();

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7">
          <SectionHeader number={1} title={t('onboarding.step1.title')} />

          <FormField
            control={form.control}
            name="icNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.icNumber')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t('onboarding.step1.icNumberPlaceholder')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="officialEmail"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.officialEmail')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder={t('onboarding.step1.officialEmailPlaceholder')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="registrationCertificate"
            render={({ field: { onChange, name, onBlur, ref } }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.registrationCertificate')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => onChange(e.target.files)}
                    name={name}
                    onBlur={onBlur}
                    ref={ref}
                  />
                </FormControl>
                <p className="text-xs text-muted-foreground">
                  {t('onboarding.step1.registrationCertificateHelp')}
                </p>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lifListFiles"
            render={({ field: { onChange, name, onBlur, ref } }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.lifListFiles')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="file"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => onChange(e.target.files)}
                    name={name}
                    onBlur={onBlur}
                    ref={ref}
                  />
                </FormControl>
                <p className="text-xs text-muted-foreground">
                  {t('onboarding.step1.lifListFilesHelp')}
                </p>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="agency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step1.agency')}{' '}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t('onboarding.step1.agencyPlaceholder')}
                      />
                    </SelectTrigger>
                  </FormControl>{' '}
                  <SelectContent>
                    {AGENCY_CODES.map((agencyCode) => (
                      <SelectItem key={agencyCode} value={agencyCode}>
                        {tAgency(agencyCode)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  {t('onboarding.step1.agencyHelp')}
                </p>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end pt-6">
            <Button type="submit" className="px-8 py-3">
              {t('onboarding.step1.nextButton')}
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </form>
      </Form>
    );
  });

ContractorOnboardingStep1.displayName = 'ContractorOnboardingStep1';
