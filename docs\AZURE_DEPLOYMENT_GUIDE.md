# Azure App Service Deployment Guide

## Overview

This document provides troubleshooting steps and configuration details for deploying the SimPLE Next.js application to Azure App Service.

## Common Issues and Solutions

### 1. Application Error

**Symptoms:**

- Azure App Service shows "Application Error" page
- Application fails to start

**Solutions:**

#### Check Startup Configuration

Ensure your Azure App Service has the correct application settings:

```bash
# Required Application Settings in Azure Portal
WEBSITE_NODE_DEFAULT_VERSION=20.x
SCM_DO_BUILD_DURING_DEPLOYMENT=false
WEBSITE_RUN_FROM_PACKAGE=1
PORT=8080
```

#### Verify Deployment Package

The deployment package should include:

- `server.js` (Next.js standalone server)
- `package.json` (with correct dependencies)
- `build/` directory (compiled Next.js files)
- `node_modules/` (production dependencies)
- `.env.local` (environment variables)
- `web.config` (IIS configuration for Azure)

### 2. Port Configuration Issues

**Problem:** Application binds to wrong port

**Solution:** Azure App Service automatically sets the `PORT` environment variable. Next.js standalone automatically uses this port.

### 3. Static Assets Not Loading

**Problem:** CSS, JS, or images return 404 errors

**Solution:** Ensure the `web.config` file is included in your deployment and contains proper rewrite rules.

### 4. Environment Variables Not Working

**Problem:** Application can't connect to Supabase or other services

**Solutions:**

1. Verify environment variables are set in Azure Portal under Configuration > Application Settings
2. Check that `.env.local` is included in the deployment package
3. Ensure variable names match exactly (case-sensitive)

## Deployment Checklist

Before deploying, verify:

- [ ] Build completes successfully locally
- [ ] `output: 'standalone'` is set in `next.config.ts`
- [ ] Environment variables are configured in Azure Portal
- [ ] GitHub Actions workflow includes all required files in artifact
- [ ] `web.config` and `startup.sh` are included in deployment

## Debugging Steps

### 1. Check Application Logs

In Azure Portal:

1. Go to your App Service
2. Navigate to "Monitoring" > "Log stream"
3. Look for startup errors or runtime exceptions

### 2. Test Standalone Build Locally

```bash
# Build the application
pnpm build

# Test the standalone server locally
cd build/standalone
PORT=3000 node server.js
```

### 3. Verify Deployment Package

```bash
# Check contents of deployment artifact
unzip -l release.zip | head -20
```

Should include:

- `server.js`
- `package.json`
- `build/` directory
- `node_modules/`
- `web.config`

## Required Files

### web.config

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <handlers>
      <add name="iisnode" path="server.js" verb="*" modules="iisnode"/>
    </handlers>
    <!-- Additional configuration as provided -->
  </system.webServer>
</configuration>
```

### startup.sh

```bash
#!/bin/bash
export NODE_ENV="${NODE_ENV:-production}"
exec node server.js
```

## GitHub Actions Workflow

The correct workflow should:

1. Build the Next.js application
2. Copy environment files to standalone directory
3. Copy static assets to standalone directory
4. Include configuration files (web.config, startup.sh)
5. Create zip from standalone directory only

## Azure App Service Configuration

### Application Settings

```
WEBSITE_NODE_DEFAULT_VERSION=20.x
SCM_DO_BUILD_DURING_DEPLOYMENT=false
WEBSITE_RUN_FROM_PACKAGE=1
PORT=8080
```

### Environment Variables

Add your application-specific environment variables:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key
```

## Support Resources

- [Next.js Deployment Documentation](https://nextjs.org/docs/deployment)
- [Azure App Service Node.js Guide](https://docs.microsoft.com/en-us/azure/app-service/quickstart-nodejs)
- [Next.js Standalone Output](https://nextjs.org/docs/advanced-features/output-file-tracing)

## Common Error Messages

### "Cannot find module"

- Verify `node_modules` is included in deployment
- Check `package.json` dependencies

### "Port already in use"

- Azure automatically assigns port via `PORT` environment variable
- Don't hardcode port numbers

### "404 for static assets"

- Check `web.config` rewrite rules
- Verify static files are in correct location

### "Environment variable undefined"

- Check Azure Portal Application Settings
- Verify `.env.local` is included in deployment
