name: Build and deploy Node.js app to Azure Web App - simple-fe-prod

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  migrate-production:
    name: Run Database Migrations (Production)
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 8

      - name: Cache pnpm modules
        uses: actions/cache@v4
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-

      - name: Install dependencies
        run: pnpm install

      - name: Link to Supabase project
        run: |
          echo "Linking to production Supabase project..."
          supabase link --project-ref ${{ secrets.PROD_SUPABASE_PROJECT_REF }}
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.PROD_SUPABASE_SERVICE_ROLE_KEY }}

      - name: Check migration status
        run: |
          echo "Checking current migration status..."
          supabase migration list --linked
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.PROD_SUPABASE_SERVICE_ROLE_KEY }}

      - name: Run database migrations
        run: |
          echo "Applying pending migrations to production database..."
          supabase db push
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.PROD_SUPABASE_SERVICE_ROLE_KEY }}

      - name: Generate updated database types
        run: |
          echo "Generating updated database types..."
          supabase gen types typescript --linked > src/types/database.ts
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.PROD_SUPABASE_SERVICE_ROLE_KEY }}

      - name: Upload database types artifact
        uses: actions/upload-artifact@v4
        with:
          name: database-types
          path: src/types/database.ts

  build:
    needs: migrate-production
    name: Build
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Download database types
        uses: actions/download-artifact@v4
        with:
          name: database-types
          path: src/types/

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 8 # or your project's pnpm version

      - name: Cache pnpm modules
        uses: actions/cache@v4
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-

      # Optional: Create .env file from secrets
      - name: Create .env.local file
        run: |
          echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.PROD_SUPABASE_URL }}" >> .env.local
          echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.PROD_SUPABASE_ANON_KEY }}" >> .env.local

      - name: Install dependencies
        run: pnpm install

      - name: Build app
        run: pnpm run build

      # Prepare standalone for deployment
      - name: Prepare standalone artifact
        run: |
          cp .env.local build/standalone/
          cp -r build/static build/standalone/build/static
          cp web.config build/standalone/
          cp startup.sh build/standalone/
          chmod +x build/standalone/startup.sh

      - name: Zip build artifact (standalone)
        run: |
          cd build/standalone
          zip -r ../../release.zip .
        working-directory: .

      - name: Upload artifact for deployment
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: release.zip

  deploy-production:
    name: Deploy to Azure Web App (Production)
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'production'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}
    permissions:
      id-token: write
      contents: read

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Unzip artifact for deployment
        run: unzip release.zip

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_PROD }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_PROD }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_PROD }}

      - name: Deploy to Azure Web App
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'simple-fe-prod'
          slot-name: 'Production'
          package: .

      # Health check after deployment
      - name: Wait for deployment
        run: sleep 30

      - name: Health check
        run: |
          curl -f ${{ steps.deploy-to-webapp.outputs.webapp-url }} || exit 1
