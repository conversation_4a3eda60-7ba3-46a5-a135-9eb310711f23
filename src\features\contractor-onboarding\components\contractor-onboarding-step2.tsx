import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { ChevronLeft } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { RadioGroup } from '@/components/ui/radio-group';
import { SectionHeader } from '@/components/ui/section-header';
import { CompanyRegistrationOption } from './company-registration-option';
import {
  useContractorTranslations,
  useCommonTranslations,
} from '@/hooks/use-translations';
import { type ContractorStep2FormValues } from '../schemas/contractor-onboarding-schemas';

interface ContractorOnboardingStep2Props {
  form: UseFormReturn<ContractorStep2FormValues>;
  onSubmit: (values: ContractorStep2FormValues) => void;
  onPrevious: () => void;
  companyRegistrationType: 'create' | 'join';
}

export const ContractorOnboardingStep2 =
  React.memo<ContractorOnboardingStep2Props>(
    ({ form, onSubmit, onPrevious, companyRegistrationType }) => {
      const t = useContractorTranslations();
      const tCommon = useCommonTranslations();

      return (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7">
            <SectionHeader number={2} title={t('onboarding.step2.title')} />

            <FormField
              control={form.control}
              name="companyRegistrationType"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="space-y-4"
                    >
                      <CompanyRegistrationOption
                        value="create"
                        id="create-company"
                        icon={
                          <svg
                            className="w-5 h-5 text-primary-foreground"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                            />
                          </svg>
                        }
                        title={t('onboarding.step2.createTitle')}
                        description={t('onboarding.step2.createDescription')}
                      />

                      <CompanyRegistrationOption
                        value="join"
                        id="join-company"
                        icon={
                          <svg
                            className="w-5 h-5 text-primary-foreground"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                            />
                          </svg>
                        }
                        title={t('onboarding.step2.joinTitle')}
                        description={t('onboarding.step2.joinDescription')}
                      />
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Special Code Field - Only show when joining a company */}
            {companyRegistrationType === 'join' && (
              <FormField
                control={form.control}
                name="specialCode"
                render={({ field }) => (
                  <FormItem className="mt-6">
                    <FormLabel>
                      {t('onboarding.step2.specialCode')}{' '}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t(
                          'onboarding.step2.specialCodePlaceholder',
                        )}
                        {...field}
                      />
                    </FormControl>
                    <p className="text-xs text-muted-foreground">
                      {t('onboarding.step2.specialCodeHelp')}
                    </p>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onPrevious}
                className="px-6 py-3"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                {tCommon('back')}
              </Button>

              <Button
                type="submit"
                className="px-8 py-3"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting
                  ? tCommon('loading')
                  : companyRegistrationType === 'create'
                    ? t('onboarding.step2.continueToCreation')
                    : t('onboarding.step2.joinCompanyButton')}
              </Button>
            </div>
          </form>
        </Form>
      );
    },
  );

ContractorOnboardingStep2.displayName = 'ContractorOnboardingStep2';
