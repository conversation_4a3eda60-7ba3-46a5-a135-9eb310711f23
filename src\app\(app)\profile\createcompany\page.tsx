'use client';

import * as React from 'react';
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import * as LabelPrimitive from '@radix-ui/react-label';
import * as SeparatorPrimitive from '@radix-ui/react-separator';

function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        outline:
          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
        secondary:
          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
        ghost:
          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-9 px-4 py-2 has-[>svg]:px-3',
        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',
        icon: 'size-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : 'button';

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

function Input({ className, type, ...props }: React.ComponentProps<'input'>) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
        className,
      )}
      {...props}
    />
  );
}

function Label({
  className,
  ...props
}: React.ComponentProps<typeof LabelPrimitive.Root>) {
  return (
    <LabelPrimitive.Root
      data-slot="label"
      className={cn(
        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',
        className,
      )}
      {...props}
    />
  );
}

function Separator({
  className,
  orientation = 'horizontal',
  decorative = true,
  ...props
}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {
  return (
    <SeparatorPrimitive.Root
      data-slot="separator-root"
      decorative={decorative}
      orientation={orientation}
      className={cn(
        'bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px',
        className,
      )}
      {...props}
    />
  );
}

export default function FormLayout01() {
  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Building Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <svg
          className="w-full h-full"
          viewBox="0 0 1000 800"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Building Silhouettes */}
          <rect x="50" y="300" width="80" height="500" fill="currentColor" />
          <rect x="150" y="200" width="60" height="600" fill="currentColor" />
          <rect x="230" y="350" width="90" height="450" fill="currentColor" />
          <rect x="340" y="180" width="70" height="620" fill="currentColor" />
          <rect x="430" y="280" width="85" height="520" fill="currentColor" />
          <rect x="540" y="150" width="75" height="650" fill="currentColor" />
          <rect x="640" y="320" width="80" height="480" fill="currentColor" />
          <rect x="740" y="250" width="65" height="550" fill="currentColor" />
          <rect x="830" y="400" width="90" height="400" fill="currentColor" />

          {/* Windows */}
          {Array.from({ length: 50 }).map((_, i) => (
            <rect
              key={i}
              x={60 + (i % 10) * 80 + Math.random() * 10}
              y={320 + Math.floor(i / 10) * 40}
              width="8"
              height="12"
              fill="rgba(255,255,255,0.2)"
            />
          ))}
        </svg>
      </div>

      {/* Floating geometric shapes */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-white/5 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-blue-500/10 rounded-full blur-lg animate-pulse delay-1000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-40 h-40 bg-purple-500/5 rounded-full blur-2xl animate-pulse delay-2000"></div>
      </div>
      <div className="relative z-10 container mx-auto px-4 py-8 lg:py-12">
        <div className="w-full max-w-5xl mx-auto">
          {/* Form Card */}
          <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-8 lg:p-12 border border-white/20">
            {/* Horizontal Layout for Desktop */}
            <div className="grid xl:grid-cols-1 gap-8 lg:gap-12">
              {/* Form Header */}
              <form action="#" method="post">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Left Column - Basic Information & Category */}
                  <div className="space-y-8">
                    <div className="w-25 h-25 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <svg
                        className="w-12 h-12 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                        />
                      </svg>
                    </div>
                    {/* Basic Information Section */}
                    <div className="space-y-6 pt-2">
                      <div className="border-l-4 border-gray-500 pl-4">
                        <h3 className="text-xl font-semibold text-gray-800 mb-1">
                          Company Details
                        </h3>
                        <p className="text-gray-600">
                          Basic company information
                        </p>
                      </div>
                      <div className="space-y-6">
                        <div>
                          <Label
                            htmlFor="agency"
                            className="text-sm font-semibold text-gray-700 mb-2 block"
                          >
                            Agency
                            <span className="text-red-500 ml-1">*</span>
                          </Label>
                          <Input
                            type="text"
                            id="agency"
                            name="agency"
                            placeholder="Enter your agency name"
                            className="mt-1 bg-gray-50 border-gray-300 text-gray-900 placeholder:text-gray-500 focus:border-gray-500 focus:ring-gray-200 transition-all duration-200 hover:bg-gray-100"
                            required
                          />
                        </div>

                        <div>
                          <Label
                            htmlFor="competent-person-name"
                            className="text-sm font-semibold text-gray-700 mb-2 block"
                          >
                            Name of Competent Person (OYK)
                            <span className="text-red-500 ml-1">*</span>
                          </Label>
                          <Input
                            type="text"
                            id="competent-person-name"
                            name="competent-person-name"
                            autoComplete="name"
                            placeholder="Enter competent person's full name"
                            className="mt-1 bg-gray-50 border-gray-300 text-gray-900 placeholder:text-gray-500 focus:border-gray-500 focus:ring-gray-200 transition-all duration-200 hover:bg-gray-100"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    {/* Category Section */}
                    <div className="space-y-6">
                      <div className="border-l-4 border-gray-400 pl-4">
                        <h3 className="text-xl font-semibold text-gray-800 mb-1">
                          Category Selection
                        </h3>
                        <p className="text-gray-600">
                          Choose applicable categories
                        </p>
                      </div>

                      <div className="bg-gray-50 rounded-xl p-6 border border-gray-200 backdrop-blur-sm">
                        <Label className="text-sm font-semibold text-gray-700 mb-4 block">
                          Category
                          <span className="text-red-500 ml-1">*</span>
                        </Label>
                        <div className="grid grid-cols-1 gap-4">
                          {['CP1', 'CP2', 'CP3'].map((category) => (
                            <label
                              key={category}
                              className="flex items-center p-4 bg-white rounded-lg border border-gray-200 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 cursor-pointer group shadow-sm"
                            >
                              <input
                                type="checkbox"
                                name="category"
                                value={category}
                                className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded transition-colors duration-200"
                              />
                              <span className="ml-3 text-sm font-medium text-gray-700 group-hover:text-gray-900">
                                {category}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Personal Details & Documents */}
                  <div className="space-y-8">
                    {/* Personal Details Section */}
                    <div className="space-y-6">
                      <div className="border-l-4 border-gray-500 pl-4">
                        <h3 className="text-xl font-semibold text-gray-800 mb-1">
                          Personal Details
                        </h3>
                        <p className="text-gray-600">
                          Identity and contact information
                        </p>
                      </div>

                      <div className="grid grid-cols-1 gap-6">
                        <div>
                          <Label
                            htmlFor="ic-number"
                            className="text-sm font-semibold text-gray-700 mb-2 block"
                          >
                            IC Number
                            <span className="text-red-500 ml-1">*</span>
                          </Label>
                          <Input
                            type="text"
                            id="ic-number"
                            name="ic-number"
                            placeholder="Enter IC number"
                            className="mt-1 bg-gray-50 border-gray-300 text-gray-900 placeholder:text-gray-500 focus:border-gray-500 focus:ring-gray-200 transition-all duration-200 hover:bg-gray-100"
                            required
                          />
                        </div>

                        <div>
                          <Label
                            htmlFor="no-registration-oyk"
                            className="text-sm font-semibold text-gray-700 mb-2 block"
                          >
                            No Registration OYK
                            <span className="text-red-500 ml-1">*</span>
                          </Label>
                          <Input
                            type="text"
                            id="no-registration-oyk"
                            name="no-registration-oyk"
                            placeholder="Enter OYK registration number"
                            className="mt-1 bg-gray-50 border-gray-300 text-gray-900 placeholder:text-gray-500 focus:border-gray-500 focus:ring-gray-200 transition-all duration-200 hover:bg-gray-100"
                            required
                          />
                        </div>

                        <div>
                          <Label
                            htmlFor="phone-number"
                            className="text-sm font-semibold text-gray-700 mb-2 block"
                          >
                            Phone Number
                            <span className="text-red-500 ml-1">*</span>
                          </Label>
                          <Input
                            type="tel"
                            id="phone-number"
                            name="phone-number"
                            placeholder="Enter phone number"
                            className="mt-1 bg-gray-50 border-gray-300 text-gray-900 placeholder:text-gray-500 focus:border-gray-500 focus:ring-gray-200 transition-all duration-200 hover:bg-gray-100"
                            required
                          />
                        </div>

                        <div>
                          <Label
                            htmlFor="email"
                            className="text-sm font-semibold text-gray-700 mb-2 block"
                          >
                            Email Address
                            <span className="text-red-500 ml-1">*</span>
                          </Label>
                          <Input
                            type="email"
                            id="email"
                            name="email"
                            placeholder="Enter email address"
                            className="mt-1 bg-gray-50 border-gray-300 text-gray-900 placeholder:text-gray-500 focus:border-gray-500 focus:ring-gray-200 transition-all duration-200 hover:bg-gray-100"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    {/* Documents Section */}
                    <div className="space-y-6">
                      <div className="border-l-4 border-gray-400 pl-4">
                        <h3 className="text-xl font-semibold text-gray-800 mb-1">
                          Required Documents
                        </h3>
                        <p className="text-gray-600">
                          Upload necessary certification files
                        </p>
                      </div>

                      <div className="space-y-6">
                        <div className="bg-gray-50 rounded-xl p-6 border border-gray-200 backdrop-blur-sm">
                          <Label
                            htmlFor="listing-of-lift"
                            className="text-sm font-semibold text-gray-700 mb-3 block"
                          >
                            Listing of Lift with Your Service
                            <span className="text-red-500 ml-1">*</span>
                          </Label>
                          <div className="space-y-3">
                            <Input
                              type="file"
                              id="listing-of-lift"
                              name="listing-of-lift"
                              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                              className="bg-white border-gray-300 text-gray-900 file:bg-gray-100 file:text-gray-700 file:border-gray-300 file:rounded-md file:px-3 file:py-1 hover:file:bg-gray-200 transition-all duration-200"
                              required
                            />
                            <div className="flex items-center space-x-2 text-xs text-gray-600">
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                              </svg>
                              <span>
                                Maximum file size: 1MB | Formats: PDF, DOC,
                                DOCX, JPG, PNG
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-gray-50 rounded-xl p-6 border border-gray-200 backdrop-blur-sm">
                          <Label
                            htmlFor="certification-registration-oyk"
                            className="text-sm font-semibold text-gray-700 mb-3 block"
                          >
                            Certification of Registration OYK
                            <span className="text-red-500 ml-1">*</span>
                          </Label>
                          <div className="space-y-3">
                            <Input
                              type="file"
                              id="certification-registration-oyk"
                              name="certification-registration-oyk"
                              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                              className="bg-white border-gray-300 text-gray-900 file:bg-gray-100 file:text-gray-700 file:border-gray-300 file:rounded-md file:px-3 file:py-1 hover:file:bg-gray-200 transition-all duration-200"
                              required
                            />
                            <div className="flex items-center space-x-2 text-xs text-gray-600">
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                              </svg>
                              <span>
                                Maximum file size: 1MB | Formats: PDF, DOC,
                                DOCX, JPG, PNG
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>{' '}
                <Separator className="my-8 bg-white/20" />
                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row items-center justify-end space-y-3 sm:space-y-0 sm:space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full sm:w-auto px-8 py-2.5 border-white/30 text-gray-200 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200 backdrop-blur-sm"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="w-full sm:w-auto px-8 py-2.5 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                  >
                    Submit Registration
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
