import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useForceProfileRefresh } from '@/hooks/use-force-profile-refresh';
import type { FullFormValues } from '../schemas/contractor-onboarding-schemas';

export function useCompleteContractorOnboarding() {
  const queryClient = useQueryClient();
  const router = useRouter();
  const forceProfileRefresh = useForceProfileRefresh();

  return useMutation({
    mutationKey: ['contractor-onboarding', 'complete'],
    mutationFn: async (values: FullFormValues) => {
      // Get current user
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      // Update profile with onboarding data
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          // name: values.icNumber ? `IC: ${values.icNumber}` : undefined,
          // phone_number: values.officialEmail || undefined,
          onboarding_completed: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (profileError) {
        throw new Error(`Profile update failed: ${profileError.message}`);
      }

      // Handle company operations
      if (values.companyRegistrationType === 'create') {
        // Convert company name to uppercase for consistency
        const uppercaseCompanyName = values.company_name!.toUpperCase().trim();

        // Create new contractor company
        const { data: companyData, error: companyError } = await supabase
          .from('contractor_companies')
          .insert({
            company_name: uppercaseCompanyName,
            company_type: values.company_type!,
            company_hotline: values.company_hotline || '',
            oem_name: values.oem_name || null,
            code: values.code!,
            created_by: user.id,
            updated_by: user.id,
          })
          .select()
          .single();

        if (companyError) {
          // Handle unique constraint violation
          if (
            companyError.code === '23505' &&
            companyError.message.includes('company_name')
          ) {
            throw new Error(
              `Company name "${uppercaseCompanyName}" already exists. Please choose a different name.`,
            );
          }
          throw new Error(`Company creation failed: ${companyError.message}`);
        }

        // Create contractor PIC record to associate user with the company
        const { error: contractorError } = await supabase
          .from('contractor_pics')
          .insert({
            profile_id: user.id,
            contractor_company_id: companyData.id,
            ic_number: values.icNumber,
            official_email: values.officialEmail,
            registration_cert_file_url: '', // TODO: Handle file upload
            lif_list_file_urls: [], // TODO: Handle file upload
            created_by: user.id,
          });

        if (contractorError) {
          throw new Error(
            `Contractor PIC creation failed: ${contractorError.message}`,
          );
        }

        return { type: 'create', company: companyData };
      } else if (values.companyRegistrationType === 'join') {
        // Convert special code to uppercase for consistency (company codes should be uppercase)
        const uppercaseSpecialCode = values.specialCode!.toUpperCase().trim();

        // Find contractor company by code
        const { data: companyData, error: companyLookupError } = await supabase
          .from('contractor_companies')
          .select('*')
          .eq('code', uppercaseSpecialCode)
          .single();

        if (companyLookupError || !companyData) {
          throw new Error(
            `Company not found with code "${uppercaseSpecialCode}". Please check the code and try again.`,
          );
        }

        // Check if user is already a member
        const { data: existingContractor } = await supabase
          .from('contractor_pics')
          .select('profile_id')
          .eq('contractor_company_id', companyData.id)
          .eq('profile_id', user.id)
          .single();

        if (existingContractor) {
          throw new Error('You are already a member of this company');
        }

        // Join the company as contractor PIC
        const { error: contractorError } = await supabase
          .from('contractor_pics')
          .insert({
            profile_id: user.id,
            contractor_company_id: companyData.id,
            ic_number: values.icNumber,
            official_email: values.officialEmail,
            registration_cert_file_url: '', // TODO: Handle file upload
            lif_list_file_urls: [], // TODO: Handle file upload
            created_by: user.id,
          });

        if (contractorError) {
          throw new Error(`Failed to join company: ${contractorError.message}`);
        }

        return { type: 'join', company: companyData };
      }

      return { type: 'profile_only' };
    },
    onSuccess: async (result) => {
      try {
        // Clear onboarding cookie to force refresh
        document.cookie =
          'onboarding_completed=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';

        // Set a flag to indicate recent completion for fallback refresh
        localStorage.setItem('onboarding_just_completed', 'true');

        // Show success message first
        if (result?.type === 'create') {
          toast.success(
            'Company created successfully! Welcome to your new workspace.',
          );
        } else if (result?.type === 'join') {
          toast.success('Successfully joined the company!');
        } else {
          toast.success('Profile setup completed!');
        } // Invalidate all relevant queries and wait for completion
        await Promise.all([
          queryClient.invalidateQueries({ queryKey: ['user-with-profile'] }),
          queryClient.invalidateQueries({ queryKey: ['profile'] }),
          queryClient.invalidateQueries({ queryKey: ['contractor-profile'] }),
          queryClient.invalidateQueries({ queryKey: ['contractor-companies'] }),
          queryClient.invalidateQueries({ queryKey: ['contractor-pics'] }),
          queryClient.invalidateQueries({ queryKey: ['permissions'] }),
          queryClient.invalidateQueries({ queryKey: ['user'] }),
        ]);

        // Use dedicated force refresh function for comprehensive cache clearing
        await forceProfileRefresh(); // Wait a bit longer to ensure cache is properly updated
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Navigate to profile page with refresh parameter to trigger data refresh
        router.push('/profile?refresh=true');
      } catch (error) {
        console.error('Error during post-completion cleanup:', error);
        // Still navigate even if cache cleanup fails
        router.push('/profile?refresh=true');
      }
    },
    onError: (error: Error) => {
      console.error('Onboarding completion error:', error);
      toast.error(
        error.message || 'Failed to complete onboarding. Please try again.',
      );
    },
  });
}
