-- Enable UUID extension for IDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ENUM Types
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('JKR', 'Contractor', 'Client');
EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN
    CREATE TYPE company_type AS ENUM ('COMPETENT_FIRM', 'NON_COMPETENT_FIRM', 'OEM');
EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN
    CREATE TYPE complaint_status AS ENUM ('open','on_hold','closed');
EXCEPTION WHEN duplicate_object THEN null; END $$;

-- All system users (JKR PIC, Contractor PIC, Agency PIC)
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  phone_number text,
  email text NOT NULL,
  role user_role NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid REFERENCES profiles(id),
  updated_by uuid REFERENCES profiles(id),
  deleted_by uuid REFERENCES profiles(id)
);

-- JKR PICs (System Admins/Monitoring Staff)
CREATE TABLE IF NOT EXISTS jkr_pics (
  profile_id uuid PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid REFERENCES profiles(id),
  updated_by uuid REFERENCES profiles(id),
  deleted_by uuid REFERENCES profiles(id)
);

-- Agencies (Clients), created by JKR only
CREATE TABLE IF NOT EXISTS agencies (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  code text NOT NULL UNIQUE,
  location text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid REFERENCES profiles(id), -- JKR PIC who created
  updated_by uuid REFERENCES profiles(id),
  deleted_by uuid REFERENCES profiles(id)
);

-- Agency PICs (Client PICs), join an agency by code/selection
CREATE TABLE IF NOT EXISTS agency_pics (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  agency_id uuid NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  joined_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid REFERENCES profiles(id),
  updated_by uuid REFERENCES profiles(id),
  deleted_by uuid REFERENCES profiles(id)
);

-- Contractor Companies (registered by contractor PICs)
CREATE TABLE IF NOT EXISTS contractor_companies (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_name text NOT NULL,
  company_type company_type NOT NULL,
  oem_name text,
  company_hotline text NOT NULL,
  is_active boolean NOT NULL DEFAULT true,
  code text UNIQUE,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid REFERENCES profiles(id),
  updated_by uuid REFERENCES profiles(id),
  deleted_by uuid REFERENCES profiles(id)
);

-- Contractor PICs (employees, join a company, choose one agency during onboarding)
CREATE TABLE IF NOT EXISTS contractor_pics (
  profile_id uuid PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
  contractor_company_id uuid NOT NULL REFERENCES contractor_companies(id) ON DELETE CASCADE,
  agency_id uuid REFERENCES agencies(id), -- chosen by contractor PIC on onboarding
  category text[],
  ic_number text,
  official_email text NOT NULL,
  registration_cert_file_url text NOT NULL,
  lif_list_file_urls text[] NOT NULL,
  joined_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid REFERENCES profiles(id),
  updated_by uuid REFERENCES profiles(id),
  deleted_by uuid REFERENCES profiles(id)
);

DO $$ BEGIN
    CREATE TYPE pma_status AS ENUM ('valid','validating','invalid');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;
CREATE TABLE IF NOT EXISTS pmas (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  contractor_id uuid REFERENCES profiles(id),
  pma_expiry_date date NOT NULL,
  pma_status pma_status NOT NULL DEFAULT 'validating',
  file_url text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid REFERENCES profiles(id),
  updated_by uuid REFERENCES profiles(id),
  deleted_by uuid REFERENCES profiles(id)
);

-- Lifts (elevator/lift installations)
CREATE TABLE IF NOT EXISTS lifts (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  client_id uuid REFERENCES profiles(id),
  jkr_pic_id uuid REFERENCES profiles(id),
  location text,
  lift_type text,
  brand text,
  model text,
  capacity_kg integer,
  capacity_persons integer,
  floors_served integer,
  installation_date date,
  last_inspection_date date,
  next_inspection_date date,
  status text DEFAULT 'active',
  serial_number text,
  certificate_number text,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid REFERENCES profiles(id),
  updated_by uuid REFERENCES profiles(id),
  deleted_by uuid REFERENCES profiles(id)
);

-- Daily logs (entered by contractor companies)
CREATE TABLE IF NOT EXISTS daily_logs (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  contractor_company_id uuid REFERENCES contractor_companies(id),
  lift_id uuid REFERENCES lifts(id),
  operation_log_type text NOT NULL,
  log_date date NOT NULL,
  person_in_charge_name text NOT NULL,
  person_in_charge_phone text NOT NULL,
  description text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid REFERENCES profiles(id),
  updated_by uuid REFERENCES profiles(id),
  deleted_by uuid REFERENCES profiles(id)
);

-- Complaints (raised by agency PIC, about a lift and contractor)
CREATE TABLE IF NOT EXISTS complaints (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  agency_id uuid REFERENCES agencies(id),
  contractor_company_id uuid REFERENCES contractor_companies(id),
  lift_id uuid REFERENCES lifts(id),
  status complaint_status NOT NULL DEFAULT 'open',
  description text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid REFERENCES profiles(id),
  updated_by uuid REFERENCES profiles(id),
  deleted_by uuid REFERENCES profiles(id)
);