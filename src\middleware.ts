import { createServerClient } from '@supabase/ssr';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import type { UserRole } from './types/auth';
import { locales, defaultLocale } from './i18n/config';
import {
  setUserCookies,
  getUserDataFromCookies,
  isProtectedPath,
  isAuthPath,
  isOnboardingPath,
  hasRoutePermission,
  createRedirectUrl,
  createSafeRedirectUrl,
  fetchUserProfile,
} from './lib/middleware-utils';

// Create the i18n middleware
const intlMiddleware = createMiddleware({
  locales,
  defaultLocale,
  localePrefix: 'always',
});

export async function middleware(req: NextRequest) {
  // Handle i18n routing first
  const pathname = req.nextUrl.pathname;

  // Check if the request is for a locale-specific path
  const pathnameIsMissingLocale = locales.every(
    (locale) =>
      !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`,
  );

  // Redirect if there is no locale
  if (pathnameIsMissingLocale) {
    return intlMiddleware(req);
  }

  // Extract locale from pathname
  const locale = pathname.split('/')[1];
  const pathnameWithoutLocale = pathname.replace(`/${locale}`, '') || '/';

  // Initialize Supabase client
  let supabaseResponse = NextResponse.next({ request: req });
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return req.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options: _options }) =>
            req.cookies.set(name, value),
          );
          supabaseResponse = NextResponse.next({ request: req });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options),
          );
        },
      },
    },
  );

  // Get session
  const {
    data: { session },
  } = await supabase.auth.getSession();

  const isProtected = isProtectedPath(pathnameWithoutLocale);
  const isAuth = isAuthPath(pathnameWithoutLocale);

  // Handle unauthenticated access to protected routes
  if (isProtected && !session) {
    const loginUrl = createRedirectUrl(
      `/${locale}/login`,
      pathnameWithoutLocale,
      req.nextUrl.search,
      req.url,
    );
    return NextResponse.redirect(loginUrl);
  }

  // Handle authenticated users on protected routes
  if (isProtected && session) {
    const redirectResponse = await handleProtectedRoute(
      req,
      supabase,
      session,
      supabaseResponse,
      locale,
    );
    if (redirectResponse) return redirectResponse;
  } // Handle authenticated users on auth routes
  if (isAuth && session) {
    const redirectTo = req.nextUrl.searchParams.get('redirectTo');

    // Get user role to determine appropriate default redirect
    let defaultPath = '/dashboard';
    const { userRole } = getUserDataFromCookies(req);

    if (userRole === 'Client') {
      defaultPath = '/daily-logs'; // Client users should go to daily logs by default
    }

    const redirectUrl = createSafeRedirectUrl(
      redirectTo,
      `/${locale}${defaultPath}`,
      req.url,
    );
    return NextResponse.redirect(new URL(redirectUrl, req.url));
  }

  return supabaseResponse;
}

/**
 * Handle authenticated users accessing protected routes
 */
async function handleProtectedRoute(
  req: NextRequest,
  supabase: ReturnType<typeof createServerClient>,
  session: { user: { id: string } },
  supabaseResponse: NextResponse,
  locale: string,
): Promise<NextResponse | null> {
  try {
    // Get user data from cookies or database
    let { userRole, onboardingCompleted } = getUserDataFromCookies(req);

    // Fetch from database if not in cookies
    if (
      !userRole ||
      req.cookies.get('onboarding_completed')?.value === undefined
    ) {
      const profileData = await fetchUserProfile(supabase, session.user.id);

      if (profileData) {
        userRole = profileData.userRole;
        onboardingCompleted = profileData.onboardingCompleted;

        // Store in cookies for future requests
        setUserCookies(supabaseResponse, userRole, onboardingCompleted);
      }
    }

    // Check onboarding completion
    const onboardingRedirect = handleOnboardingCheck(
      req,
      onboardingCompleted,
      locale,
    );
    if (onboardingRedirect) return onboardingRedirect;

    // Check role-based permissions
    const rbacRedirect = handleRBACCheck(req, userRole, locale);
    if (rbacRedirect) return rbacRedirect;

    return null;
  } catch (error) {
    console.error('Protected route middleware error:', error);
    return null;
  }
}

/**
 * Handle onboarding completion check
 */
function handleOnboardingCheck(
  req: NextRequest,
  onboardingCompleted: boolean,
  locale: string,
): NextResponse | null {
  const pathnameWithoutLocale =
    req.nextUrl.pathname.replace(`/${locale}`, '') || '/';

  if (!onboardingCompleted && !isOnboardingPath(pathnameWithoutLocale)) {
    return NextResponse.redirect(new URL(`/${locale}/profile`, req.url));
  }
  return null;
}

/**
 * Handle role-based access control
 */
function handleRBACCheck(
  req: NextRequest,
  userRole: UserRole | undefined,
  locale: string,
): NextResponse | null {
  if (!userRole) return null;

  const pathnameWithoutLocale =
    req.nextUrl.pathname.replace(`/${locale}`, '') || '/';

  if (!hasRoutePermission(pathnameWithoutLocale, userRole)) {
    // Redirect to appropriate default page based on user role
    let defaultPath = '/dashboard';
    if (userRole === 'Client') {
      defaultPath = '/daily-logs'; // Client users can only access daily logs and complaints
    }

    const accessDeniedUrl = new URL(`/${locale}${defaultPath}`, req.url);
    accessDeniedUrl.searchParams.set('error', 'access_denied');
    return NextResponse.redirect(accessDeniedUrl);
  }

  return null;
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
