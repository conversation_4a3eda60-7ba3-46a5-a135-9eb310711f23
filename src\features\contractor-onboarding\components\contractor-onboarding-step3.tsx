import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import {
  ChevronLeft,
  Co<PERSON>,
  Check,
  RefreshCw,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SectionHeader } from '@/components/ui/section-header';
import { useCheckCompanyNameAvailability } from '@/hooks/use-company-name-availability';
import {
  useContractorTranslations,
  useCommonTranslations,
  useCompanyTranslations,
} from '@/hooks/use-translations';
import { type ContractorStep3FormValues } from '../schemas/contractor-onboarding-schemas';

interface ContractorOnboardingStep3Props {
  form: UseFormReturn<ContractorStep3FormValues>;
  onSubmit: (values: ContractorStep3FormValues) => void;
  onPrevious: () => void;
  watchedCompanyType:
    | 'COMPETENT_FIRM'
    | 'NON_COMPETENT_FIRM'
    | 'OEM'
    | undefined;
  isCodeCopied: boolean;
  onCopyCode: () => void;
  onRegenerateCode: () => void;
}

export const ContractorOnboardingStep3 =
  React.memo<ContractorOnboardingStep3Props>(
    ({
      form,
      onSubmit,
      onPrevious,
      watchedCompanyType,
      isCodeCopied,
      onCopyCode,
      onRegenerateCode,
    }) => {
      const t = useContractorTranslations();
      const tCommon = useCommonTranslations();
      const tCompany = useCompanyTranslations();

      const companyName = form.watch('company_name');
      const { data: availabilityData, isLoading: checkingAvailability } =
        useCheckCompanyNameAvailability(companyName);

      return (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7">
            <SectionHeader number={3} title={t('onboarding.step3.title')} />

            <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 lg:gap-12">
              {/* Company Information Section */}
              <div className="xl:col-span-1">
                <div className="space-y-7">
                  {/* Auto-generated Company Code */}
                  <FormField
                    control={form.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('onboarding.step3.companyCode')}
                        </FormLabel>
                        <div className="flex gap-2">
                          <FormControl>
                            <Input
                              {...field}
                              readOnly
                              className="font-mono text-center tracking-wider bg-muted"
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={onCopyCode}
                            className="shrink-0"
                          >
                            {isCodeCopied ? (
                              <Check className="w-4 h-4" />
                            ) : (
                              <Copy className="w-4 h-4" />
                            )}
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={onRegenerateCode}
                            className="shrink-0"
                          >
                            <RefreshCw className="w-4 h-4" />
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {t('onboarding.step3.companyCodeHelp')}
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="company_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('onboarding.step3.companyName')}{' '}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              placeholder={t(
                                'onboarding.step3.companyNamePlaceholder',
                              )}
                              {...field}
                            />
                            {checkingAvailability && (
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
                              </div>
                            )}
                            {availabilityData?.available === true && (
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              </div>
                            )}
                            {availabilityData?.available === false && (
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <AlertCircle className="h-4 w-4 text-red-600" />
                              </div>
                            )}
                          </div>
                        </FormControl>
                        <div className="space-y-1">
                          <p className="text-sm text-muted-foreground">
                            {t('onboarding.step3.companyNameHelp')}
                          </p>
                          {field.value && (
                            <p className="text-sm text-primary font-medium">
                              {t('onboarding.step3.companyNamePreview', {
                                name: field.value.trim().toUpperCase(),
                              })}
                            </p>
                          )}
                          {availabilityData?.message && (
                            <p
                              className={`text-sm font-medium ${
                                availabilityData.available === true
                                  ? 'text-green-600'
                                  : availabilityData.available === false
                                    ? 'text-red-600'
                                    : 'text-muted-foreground'
                              }`}
                            >
                              {availabilityData.message}
                            </p>
                          )}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="company_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('onboarding.step3.companyType')}{' '}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder={t(
                                  'onboarding.step3.companyTypeHelp',
                                )}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="COMPETENT_FIRM">
                              {tCompany('types.competent_firm')}
                            </SelectItem>
                            <SelectItem value="NON_COMPETENT_FIRM">
                              {tCompany('types.non_competent_firm')}
                            </SelectItem>
                            <SelectItem value="OEM">
                              {tCompany('types.oem')}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="company_hotline"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('onboarding.step3.companyHotline')}{' '}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder={t(
                              'onboarding.step3.companyHotlinePlaceholder',
                            )}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* OEM Details Section - Only show if OEM is selected */}
              {watchedCompanyType === 'OEM' && (
                <div className="xl:col-span-1">
                  <div className="space-y-7">
                    <FormField
                      control={form.control}
                      name="oem_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('onboarding.step3.oemName')}{' '}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'onboarding.step3.oemNamePlaceholder',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <p className="text-xs text-muted-foreground">
                            {t('onboarding.step3.oemNameHelp')}
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onPrevious}
                className="px-6 py-3"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                {tCommon('back')}
              </Button>

              <Button
                type="submit"
                className="px-8 py-3"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting
                  ? t('onboarding.step3.creatingButton')
                  : t('onboarding.step3.createButton')}
              </Button>
            </div>
          </form>
        </Form>
      );
    },
  );

ContractorOnboardingStep3.displayName = 'ContractorOnboardingStep3';
