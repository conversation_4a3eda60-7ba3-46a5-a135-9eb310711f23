import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import { Badge } from './badge';
import { Button } from './button';
import {
  Building2,
  Phone,
  Mail,
  Calendar,
  FileText,
  Users,
  MapPin,
  ExternalLink,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import type { ContractorProfileData } from '../../hooks/use-contractor-profile';

interface ContractorDetailsProps {
  contractorData: ContractorProfileData;
  className?: string;
}

/**
 * Component to display contractor-specific details including company info and PIC details
 */
export function ContractorDetails({
  contractorData,
  className,
}: ContractorDetailsProps) {
  const { contractorPic, contractorCompany, agency } = contractorData;

  if (!contractorPic || !contractorCompany) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-amber-600" />
            <span>Contractor Profile Incomplete</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Your contractor profile setup is not complete. Please complete your
            onboarding process.
          </p>
        </CardContent>
      </Card>
    );
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getCompanyTypeDisplay = (type: string) => {
    switch (type) {
      case 'COMPETENT_FIRM':
        return 'Competent Firm';
      case 'MAINTENANCE_CONTRACTOR':
        return 'Maintenance Contractor';
      case 'INSTALLATION_CONTRACTOR':
        return 'Installation Contractor';
      default:
        return type;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Company Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5 text-blue-600" />
            <span>Company Information</span>
            {contractorCompany.is_active && (
              <Badge variant="default" className="ml-2">
                <CheckCircle className="h-3 w-3 mr-1" />
                Active
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  Company Name
                </h4>
                <p className="font-medium">{contractorCompany.company_name}</p>
              </div>

              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  Company Type
                </h4>
                <Badge variant="secondary">
                  {getCompanyTypeDisplay(contractorCompany.company_type)}
                </Badge>
              </div>

              {contractorCompany.code && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    Company Code
                  </h4>
                  <p className="font-mono text-sm">{contractorCompany.code}</p>
                </div>
              )}
            </div>

            <div className="space-y-3">
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  Company Hotline
                </h4>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-green-600" />
                  <span>{contractorCompany.company_hotline}</span>
                </div>
              </div>

              {contractorCompany.oem_name && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    OEM Name
                  </h4>
                  <p>{contractorCompany.oem_name}</p>
                </div>
              )}

              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  Registered
                </h4>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">
                    {formatDate(contractorCompany.created_at)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* PIC Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-green-600" />
            <span>Person In Charge (PIC) Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  Official Email
                </h4>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-blue-600" />
                  <span>{contractorPic.official_email}</span>
                </div>
              </div>

              {contractorPic.ic_number && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    IC Number
                  </h4>
                  <p className="font-mono text-sm">{contractorPic.ic_number}</p>
                </div>
              )}

              {contractorPic.joined_at && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    Joined Date
                  </h4>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-green-600" />
                    <span className="text-sm">
                      {formatDate(contractorPic.joined_at)}
                    </span>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-3">
              {contractorPic.category && contractorPic.category.length > 0 && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    Categories
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {contractorPic.category.map((cat, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {cat}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {agency && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    Associated Agency
                  </h4>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-red-600" />
                      <span className="font-medium">{agency.name}</span>
                    </div>
                    {agency.code && (
                      <p className="text-sm text-muted-foreground ml-6">
                        Code: {agency.code}
                      </p>
                    )}
                    {agency.location && (
                      <p className="text-sm text-muted-foreground ml-6">
                        {agency.location}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-purple-600" />
            <span>Documents</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-sm text-muted-foreground mb-2">
                Registration Certificate
              </h4>
              <Button variant="outline" size="sm" asChild>
                <a
                  href={contractorPic.registration_cert_file_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-2"
                >
                  <FileText className="h-4 w-4" />
                  <span>View Certificate</span>
                  <ExternalLink className="h-3 w-3" />
                </a>
              </Button>
            </div>

            {contractorPic.lif_list_file_urls &&
              contractorPic.lif_list_file_urls.length > 0 && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-2">
                    LIF List Files ({contractorPic.lif_list_file_urls.length})
                  </h4>
                  <div className="space-y-2">
                    {contractorPic.lif_list_file_urls.map((url, index) => (
                      <Button key={index} variant="outline" size="sm" asChild>
                        <a
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-2"
                        >
                          <FileText className="h-4 w-4" />
                          <span>LIF List {index + 1}</span>
                          <ExternalLink className="h-3 w-3" />
                        </a>
                      </Button>
                    ))}
                  </div>
                </div>
              )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
