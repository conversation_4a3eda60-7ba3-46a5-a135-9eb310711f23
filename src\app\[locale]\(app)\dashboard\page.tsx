'use client';

import { Skeleton } from '@/components/ui/skeleton';

function DashboardContent() {
  return (
    <div className="max-w-4xl">
      {/* Welcome Header Skeleton */}
      <div className="mb-8">
        <Skeleton className="h-9 w-64 mb-2" />
        <Skeleton className="h-5 w-48" />
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Card 1 */}
        <div className="bg-card text-card-foreground p-6 rounded-lg border">
          <Skeleton className="h-6 w-32 mb-4" />
          <div className="space-y-3">
            <div>
              <Skeleton className="h-4 w-16 mb-1" />
              <Skeleton className="h-4 w-48" />
            </div>
            <div>
              <Skeleton className="h-4 w-12 mb-1" />
              <Skeleton className="h-4 w-32" />
            </div>
            <div>
              <Skeleton className="h-4 w-14 mb-1" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        </div>

        {/* Card 2 */}
        <div className="bg-card text-card-foreground p-6 rounded-lg border">
          <Skeleton className="h-6 w-28 mb-4" />
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        {/* Card 3 */}
        <div className="bg-card text-card-foreground p-6 rounded-lg border">
          <Skeleton className="h-6 w-36 mb-4" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-full" />
          </div>
        </div>

        {/* Card 4 */}
        <div className="bg-card text-card-foreground p-6 rounded-lg border">
          <Skeleton className="h-6 w-24 mb-4" />
          <div className="space-y-3">
            <div>
              <Skeleton className="h-3 w-20 mb-1" />
              <Skeleton className="h-3 w-full" />
            </div>
            <div>
              <Skeleton className="h-3 w-24 mb-1" />
              <Skeleton className="h-3 w-32" />
            </div>
            <div>
              <Skeleton className="h-3 w-18 mb-1" />
              <Skeleton className="h-3 w-28" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function DashboardPage() {
  return <DashboardContent />;
}
