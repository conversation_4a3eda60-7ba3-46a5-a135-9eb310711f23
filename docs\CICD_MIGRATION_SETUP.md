# CI/CD Pipeline with Database Migrations Setup

## Overview

This document describes the automated CI/CD pipeline that includes database migrations for the SimPLE frontend project. The pipeline deploys to both staging and production environments with proper database migration management.

## Pipeline Architecture

### Staging Pipeline (develop branch → staging)

1. **migrate-staging** → Applies database migrations to staging
2. **build** → Builds the Next.js application with updated types
3. **deploy-staging** → Deploys to Azure Web App (staging)

### Production Pipeline (main branch → production)

1. **migrate-production** → Applies database migrations to production
2. **build** → Builds the Next.js application with updated types
3. **deploy-production** → Deploys to Azure Web App (production)

## Required GitHub Secrets

### Existing Secrets (already configured)

- `STG_SUPABASE_URL` - Staging Supabase project URL
- `STG_SUPABASE_ANON_KEY` - Staging Supabase anonymous key
- `PROD_SUPABASE_URL` - Production Supabase project URL
- `PROD_SUPABASE_ANON_KEY` - Production Supabase anonymous key
- Azure deployment secrets for both environments

### New Secrets Required for Migration Pipeline

#### Staging Environment

- `STG_SUPABASE_PROJECT_REF` - Staging Supabase project reference ID
- `STG_SUPABASE_SERVICE_ROLE_KEY` - Staging Supabase service role key (for migrations)

#### Production Environment

- `PROD_SUPABASE_PROJECT_REF` - Production Supabase project reference ID
- `PROD_SUPABASE_SERVICE_ROLE_KEY` - Production Supabase service role key (for migrations)

## How to Get Required Values

### Project Reference ID

1. Go to your Supabase project dashboard
2. Navigate to Settings → General
3. Copy the "Reference ID" value

### Service Role Key

1. Go to your Supabase project dashboard
2. Navigate to Settings → API
3. Copy the "service_role" key (not the anon key)

⚠️ **Important**: The service role key has full admin access to your database. Keep it secure and never expose it in your code.

## Migration Workflow

### What Happens During Migration Job

1. **Setup Environment**

   - Checkout code
   - Setup Node.js and Supabase CLI
   - Install dependencies

2. **Link to Project**

   - Links to the respective Supabase project using project reference ID
   - Uses service role key for authentication

3. **Migration Status Check**

   - Lists current migration status using `supabase migration list --linked`
   - Shows pending migrations

4. **Apply Migrations**

   - Runs `supabase db push` to apply pending migrations
   - Uses the respective environment's service role key

5. **Generate Types**

   - Generates updated TypeScript types from the database schema using `supabase gen types typescript --linked`
   - Uploads types as artifact for the build job

6. **Error Handling**
   - If migrations fail, the entire pipeline stops
   - No deployment occurs if migrations fail

### Database Type Generation

The pipeline automatically generates updated TypeScript types after applying migrations. This ensures:

- Frontend code stays in sync with database schema
- Type safety for database operations
- No manual type generation required

## Pipeline Features

### Safety Mechanisms

- **Pre-deployment validation**: Migrations run before building the app
- **Type safety**: Updated types are generated and used in build
- **Failure prevention**: Build and deployment only proceed if migrations succeed
- **Environment isolation**: Separate migration jobs for staging and production

### Error Handling

- Migration failures stop the entire pipeline
- Clear error messages for debugging
- No partial deployments if database is out of sync

## Migration Best Practices

### For Developers

1. **Test locally first**: Always test migrations locally before pushing
2. **Review migration files**: Ensure migrations are safe and reversible
3. **Small, incremental changes**: Keep migrations small and focused
4. **Documentation**: Document any breaking changes or manual steps required

### For Database Changes

1. **Backwards compatibility**: Ensure migrations don't break existing functionality
2. **Data preservation**: Always backup important data before destructive operations
3. **Rollback plan**: Have a rollback strategy for critical migrations
4. **Testing**: Test migrations on staging before production

## Local Development

### Generate Types Locally

```bash
# Generate types from local Supabase instance
pnpm run db:types:local

# Or generate types from linked remote project (after linking)
pnpm run db:types:linked
```

### Test Migrations Locally

```bash
# Start local Supabase
supabase start

# Apply migrations locally
supabase db reset

# Generate types
pnpm run db:types:local

# Link to remote project (for testing against staging/production)
pnpm run db:link:staging  # or db:link:prod

# Check migration status on linked project
pnpm run db:migration:list:linked

# Apply migrations to linked project
pnpm run db:push
```

## Monitoring and Troubleshooting

### Common Issues

1. **Migration Timeout**

   - Large migrations may timeout
   - Consider breaking into smaller migrations

2. **Type Generation Failure**

   - Check if database schema is valid
   - Verify service role key permissions

3. **Authentication Errors**
   - Verify service role key is correct
   - Check project reference ID

### Monitoring

- Check GitHub Actions logs for detailed migration output
- Monitor Supabase dashboard for migration status
- Review database logs for any errors

## Rollback Strategy

If a migration causes issues in production:

1. **Immediate**: Revert the commit that introduced the problematic migration
2. **Create hotfix**: Push a new migration that reverses the changes
3. **Manual intervention**: Use Supabase dashboard to manually fix issues if needed

## Security Considerations

- Service role keys have full database access
- Store all secrets in GitHub repository secrets (not in code)
- Regularly rotate service role keys
- Monitor database access logs
- Use environment-specific keys (never share staging/production keys)

## Next Steps

1. **Add the required secrets** to your GitHub repository
2. **Test the pipeline** by pushing to develop branch
3. **Monitor the first run** to ensure everything works correctly
4. **Document any project-specific considerations**

For questions or issues, refer to the [Supabase Team Workflow Guide](./SUPABASE_TEAM_WORKFLOW.md) or Supabase documentation.
