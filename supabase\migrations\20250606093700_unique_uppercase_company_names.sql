-- Add unique constraint to company_name and convert existing names to uppercase
-- First, update existing company names to uppercase
UPDATE contractor_companies 
SET company_name = UPPER(company_name)
WHERE company_name IS NOT NULL;

-- Add unique constraint on company_name
ALTER TABLE contractor_companies 
ADD CONSTRAINT contractor_companies_company_name_unique UNIQUE (company_name);

-- Create index for faster queries on company_name
CREATE INDEX IF NOT EXISTS idx_contractor_companies_company_name ON contractor_companies(company_name);
