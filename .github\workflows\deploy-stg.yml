name: Build and deploy Node.js app to Azure Web App - simple-fe-stg

on:
  push:
    branches:
      - develop
  workflow_dispatch:

jobs:
  migrate-staging:
    name: Run Database Migrations (Staging)
    runs-on: ubuntu-latest
    permissions:
      contents: read
    env:
      SUPABASE_ACCESS_TOKEN: ${{ secrets.STG_SUPABASE_ACCESS_TOKEN }}
      SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 8

      - name: Cache pnpm modules
        uses: actions/cache@v4
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-

      - name: Install dependencies
        run: pnpm install

      - name: Clean previous Supabase link
        run: supabase unlink || true

      - name: Link to Supabase project
        run: |
          echo "Linking to staging Supabase project..."
          supabase link --project-ref ${{ secrets.STG_SUPABASE_PROJECT_REF }}

      - name: Check migration status
        run: |
          echo "Checking current migration status..."
          supabase migration list --linked

      - name: Run database migrations
        run: |
          echo "Applying pending migrations to staging database..."
          supabase db push

      - name: Generate updated database types
        run: |
          echo "Generating updated database types..."
          supabase gen types typescript --linked > src/types/database.ts

      - name: Upload database types artifact
        uses: actions/upload-artifact@v4
        with:
          name: database-types
          path: src/types/database.ts

  build:
    needs: migrate-staging
    name: Build
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4

      - name: Download database types
        uses: actions/download-artifact@v4
        with:
          name: database-types
          path: src/types/

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 8

      - name: Cache pnpm modules
        uses: actions/cache@v4
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-

      - name: Create .env.local file
        run: |
          echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.STG_SUPABASE_URL }}" >> .env.local
          echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.STG_SUPABASE_ANON_KEY }}" >> .env.local

      - name: Install dependencies
        run: pnpm install

      - name: Build app
        run: pnpm run build

      - name: Prepare standalone artifact
        run: |
          cp .env.local build/standalone/
          cp -r build/static build/standalone/build/static
          cp web.config build/standalone/
          cp startup.sh build/standalone/
          chmod +x build/standalone/startup.sh

      - name: Zip build artifact (standalone)
        run: |
          cd build/standalone
          zip -r ../../release.zip .
        working-directory: .

      - uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: release.zip

  deploy-staging:
    name: Deploy to Azure Web App (Staging)
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'staging'
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Unzip artifact for deployment
        run: unzip release.zip

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_STG }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_STG }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_STG }}

      - name: Deploy to Azure Web App
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'simple-fe-stg'
          slot-name: 'Production'
          package: .
