-- Enhancement of complaints table to support damage complaint log forms
-- Author: <PERSON><PERSON><PERSON><PERSON> Copilot Assistant
-- Date: 2025-06-10

-- Drop existing complaints table to recreate with new structure
DROP TABLE IF EXISTS complaints CASCADE;

-- Recreate complaints table with enhanced structure for damage complaint logs
CREATE TABLE IF NOT EXISTS complaints (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Required form fields
  email text NOT NULL,
  number text NOT NULL UNIQUE, -- Auto-generated complaint number
  damage_complaint_date date NOT NULL,
  agency_id uuid REFERENCES agencies(id),
  contractor_company_name text NOT NULL,
  location text NOT NULL,
  no_pma_lif text NOT NULL, -- Format: WP PMA 1234
  damage_complaint_description text NOT NULL,
  expected_completion_date date NOT NULL,
  involves_mantrap boolean NOT NULL DEFAULT false,
  
  -- Optional completion fields
  actual_completion_date date,
  repair_completion_time time,
  cause_of_damage text,
  correction_action text,
  proof_of_repair_urls text[], -- Array of file URLs (max 5 files)
  repair_cost_rm decimal(10,2), -- Repair cost in RM
  
  -- System fields
  status complaint_status NOT NULL DEFAULT 'open',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz,
  deleted_at timestamptz,
  created_by uuid REFERENCES profiles(id),
  updated_by uuid REFERENCES profiles(id),
  deleted_by uuid REFERENCES profiles(id),
  
  -- Foreign key references (optional for flexibility)
  contractor_company_id uuid REFERENCES contractor_companies(id),
  lift_id uuid REFERENCES lifts(id)
);

-- Create index for better query performance
CREATE INDEX idx_complaints_email ON complaints(email);
CREATE INDEX idx_complaints_damage_complaint_date ON complaints(damage_complaint_date);
CREATE INDEX idx_complaints_expected_completion_date ON complaints(expected_completion_date);
CREATE INDEX idx_complaints_agency_id ON complaints(agency_id);
CREATE INDEX idx_complaints_contractor_company_id ON complaints(contractor_company_id);
CREATE INDEX idx_complaints_status ON complaints(status);
CREATE INDEX idx_complaints_created_at ON complaints(created_at DESC);

-- Function to generate complaint numbers (format: DCL-YYYY-XXXX)
CREATE OR REPLACE FUNCTION generate_complaint_number()
RETURNS text AS $$
DECLARE
  current_year text;
  sequence_num text;
  complaint_number text;
BEGIN
  current_year := EXTRACT(YEAR FROM now())::text;
  
  -- Get the next sequence number for current year
  SELECT LPAD((COUNT(*) + 1)::text, 4, '0') INTO sequence_num
  FROM complaints 
  WHERE EXTRACT(YEAR FROM created_at) = EXTRACT(YEAR FROM now());
  
  complaint_number := 'DCL-' || current_year || '-' || sequence_num;
  
  RETURN complaint_number;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate complaint number
CREATE OR REPLACE FUNCTION set_complaint_number()
RETURNS trigger AS $$
BEGIN
  IF NEW.number IS NULL OR NEW.number = '' THEN
    NEW.number := generate_complaint_number();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_complaint_number
  BEFORE INSERT ON complaints
  FOR EACH ROW
  EXECUTE FUNCTION set_complaint_number();

-- Add update timestamp trigger
CREATE OR REPLACE FUNCTION update_complaints_updated_at()
RETURNS trigger AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_complaints_updated_at
  BEFORE UPDATE ON complaints
  FOR EACH ROW
  EXECUTE FUNCTION update_complaints_updated_at();

-- Add some sample data for testing (optional)
INSERT INTO complaints (
  email,
  damage_complaint_date,
  contractor_company_name,
  location,
  no_pma_lif,
  damage_complaint_description,
  expected_completion_date,
  involves_mantrap,
  created_by
) VALUES 
(
  '<EMAIL>',
  '2025-06-01',
  'ABC Construction Sdn Bhd',
  'Menara ABC, Level 10, Kuala Lumpur',
  'WP PMA 1234',
  'Lift door malfunction causing safety concerns',
  '2025-06-15',
  false,
  (SELECT id FROM profiles LIMIT 1)
),
(
  '<EMAIL>',
  '2025-06-02',
  'XYZ Engineering Sdn Bhd',
  'Plaza XYZ, Ground Floor, Petaling Jaya',
  'WP PMA 5678',
  'Elevator motor overheating and making unusual noises',
  '2025-06-20',
  true,
  (SELECT id FROM profiles LIMIT 1)
);
