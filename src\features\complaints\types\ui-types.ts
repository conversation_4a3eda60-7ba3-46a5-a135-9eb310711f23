// Simple UI types for complaints feature
// These are temporary UI-focused types until we connect to the real database

export interface ComplaintUI {
  id: string;
  complaintNumber: string;
  email: string;
  contactNumber?: string;
  damageComplaintDate: string;
  expectedCompletionDate: string;
  actualCompletionDate?: string;
  agency: string;
  contractorCompanyName: string;
  contractorEmail?: string;
  contractorContactNumber?: string;
  location: string;
  mantrapLocation?: string;
  noPmaLif?: string;
  description?: string;
  completionNotes?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: string;
  proofOfRepairFiles?: Array<{
    name: string;
    url: string;
    size: number;
  }>;
}

export interface ComplaintTableProps {
  onViewComplaint: (complaint: ComplaintUI) => void;
  onEditComplaint?: (complaint: ComplaintUI) => void;
}

// Mock data for UI development
export const mockComplaints: ComplaintUI[] = [
  {
    id: '1',
    complaintNumber: 'DCL-2025-0001',
    email: '<EMAIL>',
    contactNumber: '+60123456789',
    damageComplaintDate: '2025-06-01',
    expectedCompletionDate: '2025-06-15',
    actualCompletionDate: '2025-06-10',
    agency: 'Agency A',
    contractorCompanyName: 'ABC Construction Sdn Bhd',
    contractorEmail: '<EMAIL>',
    contractorContactNumber: '+60198765432',
    location: 'Level 5, Block A, Office Tower',
    mantrapLocation: 'Main Entrance',
    noPmaLif: 'WP PMA 12345',
    description: 'Damage to glass door due to heavy wind',
    completionNotes: 'Replaced with reinforced glass',
    status: 'completed',
    createdAt: '2025-06-01T09:00:00Z',
    proofOfRepairFiles: [
      {
        name: 'before-repair.jpg',
        url: '#',
        size: 2048000,
      },
      {
        name: 'after-repair.jpg',
        url: '#',
        size: 1856000,
      },
    ],
  },
  {
    id: '2',
    complaintNumber: 'DCL-2025-0002',
    email: '<EMAIL>',
    damageComplaintDate: '2025-06-05',
    expectedCompletionDate: '2025-06-20',
    agency: 'Agency B',
    contractorCompanyName: 'XYZ Repairs Sdn Bhd',
    location: 'Ground Floor, Lobby Area',
    mantrapLocation: 'Side Entrance',
    noPmaLif: 'WP PMA 67890',
    description: 'Water damage to floor tiles from pipe burst',
    status: 'in_progress',
    createdAt: '2025-06-05T14:30:00Z',
  },
  {
    id: '3',
    complaintNumber: 'DCL-2025-0003',
    email: '<EMAIL>',
    contactNumber: '+60187654321',
    damageComplaintDate: '2025-06-08',
    expectedCompletionDate: '2025-06-25',
    agency: 'Ministry X',
    contractorCompanyName: 'Quick Fix Solutions',
    location: 'Parking Level B1',
    description: 'Crack in concrete wall from vehicle impact',
    status: 'pending',
    createdAt: '2025-06-08T11:15:00Z',
  },
];
