import { getRequestConfig } from 'next-intl/server';
import { defaultLocale, locales, type Locale } from './config';

export default getRequestConfig(async ({ locale }) => {
  // Debug logging
  console.log('Request config called with locale:', locale);

  // Validate that the incoming locale is supported
  const validatedLocale = locales.includes(locale as Locale)
    ? (locale as Locale)
    : defaultLocale;

  console.log('Using validated locale:', validatedLocale);

  try {
    const messages = (await import(`../../messages/${validatedLocale}.json`))
      .default;
    console.log('Messages loaded successfully for locale:', validatedLocale);
    return {
      locale: validatedLocale,
      messages,
    };
  } catch (error) {
    console.error(
      'Failed to load messages for locale:',
      validatedLocale,
      error,
    );
    // Fallback to default locale
    const fallbackMessages = (
      await import(`../../messages/${defaultLocale}.json`)
    ).default;
    return {
      locale: defaultLocale,
      messages: fallbackMessages,
    };
  }
});
