'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import type { AuthError, Session } from '@supabase/supabase-js';
import type {
  LoginCredentials,
  SignUpCredentials,
  UserRole,
} from '../types/auth';
import { updateLastLogin } from '@/lib/database';

// Hook to get current user
export function useUser() {
  return useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();
      if (error) throw error;
      return user;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get current user with profile data
export function useUserWithProfile() {
  return useQuery({
    queryKey: ['user-with-profile'],
    queryFn: async () => {
      // Get auth user
      const {
        data: { user },
        error: authError,
      } = await supabase.auth.getUser();

      if (authError) throw authError;
      if (!user) return null;

      // Get profile data
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        // PGRST116 is "not found" - acceptable if profile doesn't exist yet
        console.error('Profile fetch error:', profileError);
      }

      return {
        ...user,
        profile: profile || null,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get current session
export function useSession() {
  return useQuery({
    queryKey: ['session'],
    queryFn: async () => {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();
      if (error) throw error;
      return session;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for login
export function useLogin() {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationKey: ['auth', 'login'],
    mutationFn: async ({ email, password }: LoginCredentials) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return data;
    },
    onSuccess: async (data) => {
      // Update the user and session queries
      queryClient.setQueryData(['user'], data.user);
      queryClient.setQueryData(['session'], data.session);

      // Check if profile exists, create if missing (fallback mechanism)
      if (data.user?.id) {
        const { data: existingProfile } = await supabase
          .from('profiles')
          .select('id, onboarding_completed')
          .eq('id', data.user.id)
          .single();

        if (!existingProfile && data.user.user_metadata) {
          // Create profile from user metadata if it doesn't exist
          const profileData = {
            id: data.user.id,
            email: data.user.email || '', // Required field
            name:
              data.user.user_metadata.full_name ||
              data.user.email?.split('@')[0] ||
              'Unknown',
            phone_number: data.user.user_metadata.phone_number || null,
            role: (data.user.user_metadata.role as UserRole) || 'Client',
            onboarding_completed: false, // New users need to complete onboarding
            created_at: new Date().toISOString(),
          };

          await supabase.from('profiles').insert(profileData);
        }

        // Update last login timestamp
        try {
          await updateLastLogin(data.user.id);
        } catch (loginError) {
          console.error('Error updating last login:', loginError);
          // Don't throw - this is not critical for login success
        }

        // Check onboarding status and redirect accordingly
        if (existingProfile && !existingProfile.onboarding_completed) {
          // User has profile but hasn't completed onboarding
          router.push('/profile');
          return;
        }
      }

      // Invalidate and refetch auth-related queries to ensure fresh data
      await queryClient.invalidateQueries({ queryKey: ['user'] });
      await queryClient.invalidateQueries({ queryKey: ['session'] });
      await queryClient.invalidateQueries({ queryKey: ['user-with-profile'] });
      await queryClient.invalidateQueries({ queryKey: ['profile'] });

      // Redirect will be handled by middleware for completed onboarding
      router.refresh();
    },
    onError: (error: AuthError) => {
      console.error('Login error:', error.message);
      // Error will be handled in the component
    },
  });
}

// Hook for signup with automatic profile creation
export function useSignUp() {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationKey: ['auth', 'signup'],
    mutationFn: async ({
      email,
      password,
      fullName,
      phoneNumber,
      role,
    }: SignUpCredentials) => {
      // Step 1: Create auth user with metadata
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            phone_number: phoneNumber,
            role: role,
          },
        },
      });

      if (error) {
        console.error('Signup error:', error);
        throw error;
      }

      // Step 2: Create profile immediately for confirmed users
      if (data.user && data.user.email_confirmed_at) {
        try {
          const { error: profileError } = await supabase
            .from('profiles')
            .insert({
              id: data.user.id,
              email: email,
              name: fullName,
              phone_number: phoneNumber,
              role: role,
              onboarding_completed: false, // New users need to complete onboarding
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            });

          if (profileError) {
            console.error('Profile creation error:', profileError);
            // Don't throw - user is created, profile can be created later
            console.warn(
              'Profile creation failed, but user account exists. Profile will be created on first login.',
            );
          }
        } catch (error) {
          console.error('Post-signup profile setup error:', error);
          // Don't throw - user account is created successfully
        }
      } else {
        // User needs email confirmation - profile will be created via trigger or login
        console.log('User created, pending email confirmation');
      }

      return data;
    },
    onSuccess: (data) => {
      // Update the user and session queries if user is confirmed
      if (data.user && data.session) {
        queryClient.setQueryData(['user'], data.user);
        queryClient.setQueryData(['session'], data.session);

        // Invalidate profile-related queries
        queryClient.invalidateQueries({ queryKey: ['user-with-profile'] });
        queryClient.invalidateQueries({ queryKey: ['profile'] });

        // New users always need to complete onboarding
        router.push('/profile');
      } else {
        // Email confirmation required
        // Success message will be shown in the form component
      }
    },
  });
}

// Hook for logout
export function useLogout() {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationKey: ['auth', 'logout'],
    mutationFn: async () => {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    },
    onSuccess: () => {
      // Clear all queries
      queryClient.clear();

      // Clear role and onboarding cookies
      document.cookie =
        'user_role=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      document.cookie =
        'onboarding_completed=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';

      // Redirect to login
      router.push('/login');
      router.refresh();
    },
    onError: (error: AuthError) => {
      console.error('Logout error:', error.message);
    },
  });
}

// Hook for password reset
export function usePasswordReset() {
  return useMutation({
    mutationKey: ['auth', 'password-reset'],
    mutationFn: async (email: string) => {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });
      if (error) throw error;
    },
    onError: (error: AuthError) => {
      console.error('Password reset error:', error.message);
    },
  });
}

// Auth state listener hook with profile sync
export function useAuthStateChange() {
  const queryClient = useQueryClient();

  const handleAuthStateChange = useCallback(
    (event: string, session: Session | null) => {
      console.log('Auth state change:', event, session);

      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        queryClient.setQueryData(['session'], session);
        queryClient.setQueryData(['user'], session?.user ?? null);

        // Invalidate profile-related queries to refetch fresh data
        queryClient.invalidateQueries({ queryKey: ['user-with-profile'] });
        queryClient.invalidateQueries({ queryKey: ['profile'] });

        // Update last login if user signed in
        if (event === 'SIGNED_IN' && session?.user?.id) {
          updateLastLogin(session.user.id).catch((error) => {
            console.error('Error updating last login:', error);
          });
        }
      } else if (event === 'SIGNED_OUT') {
        queryClient.setQueryData(['session'], null);
        queryClient.setQueryData(['user'], null);
        queryClient.setQueryData(['user-with-profile'], null);

        // Clear all profile-related cache
        queryClient.removeQueries({ queryKey: ['profile'] });
      }
    },
    [queryClient],
  );

  return { handleAuthStateChange };
}
