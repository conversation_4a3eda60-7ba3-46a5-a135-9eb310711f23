/**
 * Contractor Onboarding Feature
 *
 * This feature handles the multi-step contractor onboarding process including
 * company registration, profile setup, and verification steps.
 */

// Export main components
export { ContractorOnboarding } from './components/contractor-onboarding';
export { ContractorOnboardingProgress } from './components/contractor-onboarding-progress';

// Export step components
export { ContractorOnboardingStep1 } from './components/contractor-onboarding-step1';
export { ContractorOnboardingStep2 } from './components/contractor-onboarding-step2';
export { ContractorOnboardingStep3 } from './components/contractor-onboarding-step3';

// Export company components
export { CompanyForm } from './components/company-form';
export { CreateCompanyForm } from './components/create-company-form';
export { JoinCompanyForm } from './components/join-company-form';
export { CompanyRegistrationOption } from './components/company-registration-option';

// Export hooks
export { useContractorOnboarding } from './hooks/use-contractor-onboarding';
export { useCompleteContractorOnboarding } from './hooks/use-complete-contractor-onboarding';
export { useCheckCompanyNameAvailability } from './hooks/use-company-name-availability';

// Export schemas
export * from './schemas/contractor-onboarding-schemas';
