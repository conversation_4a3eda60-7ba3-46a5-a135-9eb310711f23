# Supabase Team Collaboration Guide

This guide outlines best practices for working with teammates on the SimPLE project using Supabase, focusing on database branching techniques that prevent data loss and ensure smooth collaboration.

## Table of Contents

1. [Overview](#overview)
2. [Environment Setup](#environment-setup)
3. [Branching Strategy](#branching-strategy)
4. [Database Migration Workflow](#database-migration-workflow)
5. [Local Development Best Practices](#local-development-best-practices)
6. [Production Safety Guidelines](#production-safety-guidelines)
7. [Common Scenarios](#common-scenarios)
8. [Troubleshooting](#troubleshooting)
9. [Emergency Procedures](#emergency-procedures)

## Overview

Our Supabase setup uses:

- **Local Development**: Supabase CLI with local PostgreSQL instance
- **Migration-Based**: All schema changes tracked in SQL migration files
- **Type Safety**: Automated TypeScript type generation from database schema
- **Multiple Environments**: Local, Staging, Production

## Environment Setup

### 1. Required Tools

```bash
# Install Supabase CLI
npm install -g @supabase/cli

# Verify installation
supabase --version
```

### 2. Project Configuration

Our project is configured with:

- Project ID: `simple-fe`
- Local API Port: `54321`
- Local DB Port: `54322`
- Shadow DB Port: `54320` (for migrations)

### 3. Initial Setup for New Team Members

```bash
# Clone the repository
git clone <repository-url>
cd simple-fe

# Install dependencies
pnpm install

# Start Supabase locally
supabase start

# Apply existing migrations
supabase db reset

# Generate types
pnpm run db:types:local
```

## Branching Strategy

### Git + Database Branching Approach

We use a **feature branch + migration** strategy that ensures database changes are version-controlled and reversible.

```
main (production-ready)
├── develop (integration branch)
├── feature/user-profile-enhancement
├── feature/company-registration
└── hotfix/critical-auth-fix
```

### Database Branching Rules

1. **Never modify existing migrations** once they're merged to `develop`
2. **Always create new migrations** for schema changes
3. **Test migrations locally** before pushing
4. **Use timestamp-based migration names** (format: `YYYYMMDDHHMMSS_description.sql`)
5. **Always use `supabase migration new` command** to ensure proper timestamp generation

## Database Migration Workflow

### 1. Creating a New Migration

```bash
# Start from the latest develop branch
git checkout develop
git pull origin develop

# Create your feature branch
git checkout -b feature/add-user-preferences

# Start local Supabase (if not already running)
supabase start

# Create a new migration
supabase migration new add_user_preferences_table

# This creates: supabase/migrations/YYYYMMDDHHMMSS_add_user_preferences_table.sql
```

### 2. Writing the Migration

```sql
-- Example: supabase/migrations/20250609120000_add_user_preferences_table.sql
-- Note: Timestamp format is YYYYMMDDHHMMSS (Year-Month-Day-Hour-Minute-Second)

-- Add new table for user preferences
CREATE TABLE IF NOT EXISTS user_preferences (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  theme text DEFAULT 'light' CHECK (theme IN ('light', 'dark')),
  language text DEFAULT 'en' CHECK (language IN ('en', 'ms')),
  notifications_enabled boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),

  UNIQUE(user_id)
);

-- Add RLS policies
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own preferences" ON user_preferences
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON user_preferences
  FOR ALL USING (auth.uid() = user_id);

-- Add indexes for performance
CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_preferences_updated_at
  BEFORE UPDATE ON user_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
```

### 3. Testing the Migration

```bash
# Apply the migration locally
supabase db reset

# Verify the migration worked
supabase db diff

# Generate updated TypeScript types
pnpm run db:types:local

# Test your application
pnpm dev
```

### 4. Rollback Strategy (if needed)

```sql
-- Create rollback migration if needed
-- supabase/migrations/20250609120001_rollback_user_preferences.sql

DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON user_preferences;
DROP FUNCTION IF EXISTS update_updated_at_column();
DROP TABLE IF EXISTS user_preferences;
```

## Local Development Best Practices

### 1. Daily Workflow

```bash
# Morning routine - sync with team changes
git checkout develop
git pull origin develop
supabase db reset  # Apply all migrations fresh

# Switch to your feature branch
git checkout feature/your-feature
git rebase develop  # Get latest changes

# Start development
supabase start
pnpm dev
```

### 2. Database State Management

```bash
# Reset to clean state (applies all migrations)
supabase db reset

# Check current migration status
supabase migration list

# Apply pending migrations only
supabase db push

# Generate fresh types after schema changes
pnpm run db:types:local
```

### 3. Seed Data for Development

Create seed scripts for consistent development data:

```sql
-- supabase/seed.sql (optional)
INSERT INTO profiles (id, name, email, role) VALUES
  ('00000000-0000-0000-0000-000000000001', 'Test JKR User', '<EMAIL>', 'JKR'),
  ('00000000-0000-0000-0000-000000000002', 'Test Contractor', '<EMAIL>', 'Contractor');
```

```bash
# Apply seed data
psql -h localhost -p 54322 -d postgres -U postgres -f supabase/seed.sql
```

## Migration Naming Convention Update

**Important Note**: This project has been updated to use timestamp-based migration naming to prevent conflicts when multiple developers create migrations simultaneously.

### Current Migration Files:

- `20250603172900_initial_schema.sql` (was: `001_initial_schema.sql`)
- `20250606093600_add_onboarding_completed.sql` (was: `002_add_onboarding_completed.sql`)
- `20250606093700_unique_uppercase_company_names.sql` (was: `003_unique_uppercase_company_names.sql`)

### For New Migrations:

Always use the Supabase CLI to create new migrations:

```bash
# This automatically generates timestamp-based names
supabase migration new your_descriptive_name
# Creates: supabase/migrations/YYYYMMDDHHMMSS_your_descriptive_name.sql
```

### Benefits of Timestamp Naming:

- **No conflicts**: Each developer gets unique timestamps
- **Chronological order**: Migrations execute in correct sequence
- **Easy identification**: Timestamp shows when migration was created
- **Standard practice**: Follows Supabase and industry conventions

## Production Safety Guidelines

### 1. Migration Review Process

Before merging to `develop`:

1. **Code Review**: At least one team member reviews the migration
2. **Test Migration**: Run on a copy of production data
3. **Rollback Plan**: Have a rollback migration ready
4. **Timing**: Plan migrations during low-traffic periods

### 2. Migration Deployment Steps

```bash
# Production deployment (staging first)
supabase link --project-ref <staging-project-id>
supabase db push

# Verify in staging
# Then promote to production
supabase link --project-ref <production-project-id>
supabase db push
```

### 3. Dangerous Operations

**Never do these in production without extreme caution:**

- `DROP TABLE`
- `DROP COLUMN` with data
- Changing column types without compatibility
- Removing constraints that might have dependencies

**Safe alternatives:**

```sql
-- Instead of DROP COLUMN, mark as deprecated
ALTER TABLE users ADD COLUMN old_field_deprecated boolean DEFAULT false;
-- Remove in a future migration after confirming no usage

-- Instead of changing column type directly
ALTER TABLE users ADD COLUMN new_field_name text;
-- Migrate data, then drop old column later
```

## Common Scenarios

### Scenario 1: Multiple Developers, Same Feature

**Problem**: Two developers working on the same feature need database changes.

**Solution**:

```bash
# Developer A creates migration
supabase migration new add_user_avatar

# Developer B syncs and creates dependent migration
git pull origin feature/user-profile
supabase db reset
supabase migration new add_avatar_upload_policy
```

### Scenario 2: Conflicting Migrations

**Problem**: Two feature branches have migrations with same timestamp.

**Solution**:

```bash
# When merging, rename migration files to maintain order
mv 20250609120000_feature_a.sql 20250609120000_feature_a.sql
mv 20250609120000_feature_b.sql 20250609120001_feature_b.sql

# Update migration order and test
supabase db reset
```

### Scenario 3: Hotfix on Production

**Problem**: Critical bug needs immediate database fix.

**Solution**:

```bash
# Create hotfix branch from main
git checkout main
git checkout -b hotfix/critical-auth-fix

# Create minimal migration
supabase migration new hotfix_auth_constraint

# Test locally
supabase db reset

# Deploy to production
supabase link --project-ref <production-id>
supabase db push

# Merge back to main and develop
git checkout main
git merge hotfix/critical-auth-fix
git checkout develop
git merge main
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Migration Order Issues

```bash
# Error: migration timestamp conflicts
# Solution: Rename migration files to correct order
ls supabase/migrations/
# Rename files with proper sequence numbers
```

#### 2. Local Database Out of Sync

```bash
# Error: local schema doesn't match remote
# Solution: Reset local database
supabase stop
supabase start
supabase db reset
```

#### 3. Type Generation Failures

```bash
# Error: TypeScript types are outdated
# Solution: Regenerate types
supabase start
pnpm run db:types:local
```

#### 4. Permission Errors

```bash
# Error: RLS policies preventing access
# Solution: Check and update RLS policies
supabase db shell
# Then run SQL to check policies:
# \d+ your_table_name
```

### Debug Commands

```bash
# Check Supabase status
supabase status

# View logs
supabase logs

# Connect to local database
supabase db shell

# Check migration status
supabase migration list

# View current schema
supabase db diff
```

## Emergency Procedures

### Database Corruption Recovery

1. **Stop all development work**
2. **Assess the damage**:
   ```bash
   supabase db shell
   # Check table integrity
   SELECT * FROM information_schema.tables;
   ```
3. **Restore from backup**:
   ```bash
   # If using Supabase Cloud, restore from dashboard
   # If local, restore from git migrations
   supabase db reset
   ```

### Rollback Production Migration

1. **Create immediate rollback migration**
2. **Test rollback locally**:
   ```bash
   supabase migration new rollback_problematic_change
   supabase db reset
   ```
3. **Deploy rollback**:
   ```bash
   supabase link --project-ref <production-id>
   supabase db push
   ```

### Data Loss Prevention Checklist

- [ ] Always backup before major migrations
- [ ] Test migrations on staging data
- [ ] Have rollback plans ready
- [ ] Use transactions in migrations when possible
- [ ] Monitor application after deployments
- [ ] Keep team informed of database changes

## Best Practices Summary

1. **Always use migrations** for schema changes
2. **Never edit existing migrations** once merged
3. **Test locally first**, always
4. **Communicate schema changes** with the team
5. **Use descriptive names** for migrations
6. **Plan rollback strategies** before deploying
7. **Monitor production** after deployments
8. **Keep local environment synced** with team changes

## Useful Commands Reference

```bash
# Supabase CLI Commands
supabase start                    # Start local development
supabase stop                     # Stop local development
supabase status                   # Check service status
supabase db reset                 # Reset to latest migrations
supabase db push                  # Push pending migrations
supabase migration new <name>     # Create new migration
supabase migration list           # List all migrations
supabase db diff                  # Show schema differences
supabase db shell                 # Connect to database
supabase gen types typescript --local > src/types/database.ts  # Generate types

# Git Commands for Database Work
git checkout develop              # Switch to develop branch
git pull origin develop           # Get latest changes
git checkout -b feature/new-feat  # Create feature branch
git add supabase/migrations/      # Stage migration files
git commit -m "Add: user preferences table"  # Commit changes
```

---

**Remember**: Database changes are permanent and affect the whole team. Always communicate, test thoroughly, and have a rollback plan ready!
