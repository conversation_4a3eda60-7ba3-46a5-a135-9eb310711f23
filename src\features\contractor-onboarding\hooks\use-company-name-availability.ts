import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';

export function useCheckCompanyNameAvailability(companyName: string) {
  return useQuery({
    queryKey: ['company-name-availability', companyName],
    queryFn: async () => {
      if (!companyName || companyName.trim().length < 2) {
        return { available: null, message: '' };
      }

      const uppercaseCompanyName = companyName.trim().toUpperCase();

      const { data, error } = await supabase
        .from('contractor_companies')
        .select('id')
        .eq('company_name', uppercaseCompanyName)
        .maybeSingle();

      if (error) {
        console.error('Error checking company name availability:', error);
        return { available: null, message: 'Unable to check availability' };
      }

      const available = !data;
      return {
        available,
        message: available
          ? `"${uppercaseCompanyName}" is available`
          : `"${uppercaseCompanyName}" is already taken`,
      };
    },
    enabled: <PERSON><PERSON>an(companyName && companyName.trim().length >= 2),
    staleTime: 30000, // 30 seconds
    retry: false,
  });
}
