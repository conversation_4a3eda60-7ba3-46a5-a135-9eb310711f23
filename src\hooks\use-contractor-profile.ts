import { useQuery } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import type { Database } from '../types/database';

// Type definitions for contractor data
type ContractorPic = Database['public']['Tables']['contractor_pics']['Row'];
type ContractorCompany =
  Database['public']['Tables']['contractor_companies']['Row'];
type Agency = Database['public']['Tables']['agencies']['Row'];

export interface ContractorProfileData {
  contractorPic: ContractorPic | null;
  contractorCompany: ContractorCompany | null;
  agency: Agency | null;
}

/**
 * Hook to get contractor-specific profile data including contractor_pics and contractor_companies
 * Only fetches data if the user is a contractor
 */
export function useContractorProfile(userId?: string, isContractor?: boolean) {
  return useQuery({
    queryKey: ['contractor-profile', userId],
    queryFn: async (): Promise<ContractorProfileData> => {
      if (!userId) throw new Error('User ID is required');

      // Get contractor_pics data with related contractor_companies and agencies
      const { data: contractorPic, error: contractorPicError } = await supabase
        .from('contractor_pics')
        .select(
          `
          *,
          contractor_companies!inner (
            id,
            company_name,
            company_type,
            company_hotline,
            oem_name,
            code,
            is_active,
            created_at,
            updated_at
          ),
          agencies (
            id,
            name,
            code,
            location,
            created_at,
            updated_at
          )
        `,
        )
        .eq('profile_id', userId)
        .is('deleted_at', null)
        .maybeSingle();

      if (contractorPicError && contractorPicError.code !== 'PGRST116') {
        console.error('Error fetching contractor pic:', contractorPicError);
        throw contractorPicError;
      }

      // Extract the related data (handle both single object and array cases)
      const contractorCompany = Array.isArray(
        contractorPic?.contractor_companies,
      )
        ? contractorPic?.contractor_companies[0]
        : contractorPic?.contractor_companies || null;

      const agency = Array.isArray(contractorPic?.agencies)
        ? contractorPic?.agencies[0]
        : contractorPic?.agencies || null;

      return {
        contractorPic: contractorPic
          ? ({
              ...contractorPic,
              // Remove nested data to avoid type conflicts
              contractor_companies: undefined,
              agencies: undefined,
            } as ContractorPic)
          : null,
        contractorCompany,
        agency,
      };
    },
    enabled: !!userId && !!isContractor,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get contractor company details by company ID
 */
export function useContractorCompany(companyId?: string) {
  return useQuery({
    queryKey: ['contractor-company', companyId],
    queryFn: async (): Promise<ContractorCompany | null> => {
      if (!companyId) throw new Error('Company ID is required');

      const { data, error } = await supabase
        .from('contractor_companies')
        .select('*')
        .eq('id', companyId)
        .is('deleted_at', null)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Company not found
        }
        throw error;
      }

      return data;
    },
    enabled: !!companyId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get all contractor companies (for dropdowns, etc.)
 */
export function useContractorCompanies() {
  return useQuery({
    queryKey: ['contractor-companies'],
    queryFn: async (): Promise<ContractorCompany[]> => {
      const { data, error } = await supabase
        .from('contractor_companies')
        .select('*')
        .is('deleted_at', null)
        .eq('is_active', true)
        .order('company_name', { ascending: true });

      if (error) throw error;
      return data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}
