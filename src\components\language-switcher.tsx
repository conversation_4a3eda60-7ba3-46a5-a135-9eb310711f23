'use client';

import { useLocale } from 'next-intl';
import { usePathname as useNextPathname } from 'next/navigation';
import { useRouter } from '@/i18n/navigation';
import { useNavigationTranslations } from '@/hooks/use-translations';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import { Globe } from 'lucide-react';
import { useState, useEffect } from 'react';

const languages = ['en', 'ms'] as const;

export function LanguageSwitcher() {
  const nextIntlLocale = useLocale();
  const nextPathname = useNextPathname();
  const router = useRouter();
  const t = useNavigationTranslations();
  const [currentLocale, setCurrentLocale] = useState(nextIntlLocale);

  // Extract locale from URL as fallback
  useEffect(() => {
    const urlLocale = nextPathname.split('/')[1];

    // Debug logging - you can remove this later
    console.log('LanguageSwitcher Debug:', {
      nextIntlLocale,
      urlLocale,
      nextPathname,
      isValidUrlLocale: languages.includes(
        urlLocale as (typeof languages)[number],
      ),
    });

    if (languages.includes(urlLocale as (typeof languages)[number])) {
      setCurrentLocale(urlLocale as (typeof languages)[number]);
    } else {
      setCurrentLocale(nextIntlLocale);
    }
  }, [nextPathname, nextIntlLocale]);

  const handleLanguageChange = (newLocale: string) => {
    // Get the pathname without locale for proper navigation
    const currentPath = nextPathname.replace(/^\/[a-z]{2}(\/|$)/, '/');
    const cleanPath = currentPath === '/' ? '/dashboard' : currentPath;

    router.replace(cleanPath, { locale: newLocale });
  };

  const getCurrentLanguageName = () => {
    return t(`languages.${currentLocale}` as keyof typeof t);
  };

  return (
    <Select value={currentLocale} onValueChange={handleLanguageChange}>
      <SelectTrigger className="w-full h-8 text-sm">
        <div className="flex items-center gap-2">
          <Globe className="h-3.5 w-3.5 shrink-0" />
          <span className="truncate">{getCurrentLanguageName()}</span>
        </div>
      </SelectTrigger>
      <SelectContent>
        {languages.map((code) => (
          <SelectItem key={code} value={code} className="text-sm">
            {t(`languages.${code}` as keyof typeof t)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
